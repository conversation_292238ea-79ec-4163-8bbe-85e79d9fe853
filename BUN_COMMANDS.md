# Bun Commands Reference

This document lists all available Bun commands for the codeim project.

## 🚀 Quick Start

```bash
# Install Bun (if not already installed)
curl -fsSL https://bun.sh/install | bash

# Setup the entire project
bun run setup

# Start development server
bun run dev
```

## 📦 Available Commands

### Setup & Installation
```bash
bun run setup              # Install all dependencies (Python + JS/TS)
bun install               # Install only JS/TS dependencies
bun run install:python    # Install only Python dependencies
```

### Development
```bash
bun run dev               # Start development server (memory storage)
bun run start             # Start production server
bun run server            # Alias for start
bun run server:dev        # Alias for dev
```

### Code Quality
```bash
bun run test              # Run Python tests
bun run lint              # Run Python linting (flake8)
bun run format            # Format Python code (black)
bun run check             # Run both lint and test
```

### Maintenance
```bash
bun run clean             # Clean build artifacts and cache
```

## 🐍 Python Commands (via Bun)

All Python commands are wrapped in Bun scripts for consistency:

```bash
# These commands automatically detect python3/python
bun run start             # python3 mcp_server.py
bun run test              # python3 -m pytest tests/ -v
bun run lint              # python3 -m flake8 *.py
bun run format            # python3 -m black *.py
```

## 🔧 Direct Usage

You can still use Python commands directly:

```bash
# MCP CLI Wrapper
python3 mcp_cli.py <command>

# Examples:
python3 mcp_cli.py gemini code --help
python3 mcp_cli.py claude code "write a function"
```

## 📁 Project Structure

```
codeim/
├── scripts/              # Bun TypeScript scripts
│   ├── setup.ts         # Project setup script
│   └── dev.ts           # Development server script
├── mcp_cli.py           # CLI wrapper (Python)
├── mcp_server.py        # MCP server (Python)
├── package.json         # Bun/Node.js configuration
├── bunfig.toml          # Bun configuration
├── tsconfig.json        # TypeScript configuration
├── requirements.txt     # Python dependencies
└── .env.example         # Environment variables template
```

## 🌟 Benefits of Using Bun

1. **Speed**: Faster than npm/yarn for package management
2. **Simplicity**: Single tool for multiple tasks
3. **TypeScript**: Native TypeScript support
4. **Cross-platform**: Works on macOS, Linux, and Windows
5. **Modern**: Uses latest JavaScript standards

## 🔄 Migration from npm

If you were using npm before:

| npm command | bun equivalent |
|-------------|----------------|
| `npm install` | `bun install` |
| `npm run start` | `bun run start` |
| `npm run dev` | `bun run dev` |
| `npm test` | `bun run test` |

## 🚨 Troubleshooting

### Python not found
If you get "Python not found" errors:
1. Install Python 3.8+
2. Ensure `python3` or `python` is in your PATH
3. Run `bun run setup` again

### Permission errors
If you get permission errors during Python package installation:
```bash
# Use virtual environment (recommended)
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
bun run setup
```

### Bun not found
Install Bun:
```bash
# macOS/Linux
curl -fsSL https://bun.sh/install | bash

# Windows
powershell -c "irm bun.sh/install.ps1 | iex"
```
