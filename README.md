# MCP Mobile CLI Bridge

This project provides a lightweight bridge that enables remote interaction with AI CLI tools (like Claude Code or Gemini CLI) through mobile messaging platforms (Telegram/Discord).

## Prerequisites

- Python 3.8+
- Telegram Bot Token (from @BotFather)
- Redis (for production) or local storage (for development)

For Python dependencies, see `requirements.txt`.

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd mcp-mobile-cli-bridge
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
   
   Or if you're using Node.js ecosystem tools:
   ```bash
   npm install
   ```

## Setup

1. **Create Telegram Bot**
   - Message @BotFather on Telegram
   - Create a new bot and save the token

2. **Configure Environment**
   Create a `.env` file or export environment variables:
   ```bash
   export TELEGRAM_BOT_TOKEN="your_bot_token_here"
   export MCP_SERVER_URL="http://localhost:8000"  # URL where mcp_server.py will run
   export REDIS_URL="redis://localhost:6379"  # Optional for development
   export USE_MEMORY_STORAGE=true  # For development without Redis
   ```

3. **Start MCP Server**
   ```bash
   python mcp_server.py
   ```
   
   For development:
   ```bash
   export USE_MEMORY_STORAGE=true
   python mcp_server.py
   ```

4. **Set up Telegram Webhook**
   The server needs to receive updates from Telegram. You'll need to set up a webhook.
   
   For local development, you can use a tool like [ngrok](https://ngrok.com/) to expose your local server to the internet:
   ```bash
   ngrok http 8000
   ```
   
   Then set the webhook URL with the ngrok URL:
   ```bash
   curl -F "url=https://<your-ngrok-url>/bot/webhook" https://api.telegram.org/bot<your-bot-token>/setWebhook
   ```

## Usage

1. Run your AI CLI tool through the wrapper:
   ```bash
   python mcp_cli.py gemini code --help
   # Or with claude
   python mcp_cli.py claude code --help
   ```

2. The wrapper generates a unique session ID and displays instructions

3. Send the activation command to your Telegram bot:
   ```
   /start [session_id]
   ```

4. When the AI tool needs input, you'll receive a notification on Telegram

5. Reply directly in Telegram (using buttons or text) to provide input to the CLI tool

## Development

### Project Structure
```
mcp-mobile-cli-bridge/
├── mcp_cli.py          # CLI wrapper script
├── mcp_server.py       # Core MCP server
├── requirements.txt    # Python dependencies
├── package.json        # Node.js dependencies (if applicable)
└── README.md           # This file
```

### Running Tests
```bash
pytest tests/
```

### Local Development
For local development without Redis:
```bash
export USE_MEMORY_STORAGE=true
python mcp_server.py
```

## API Endpoints

- `POST /api/session/register` - Register session with user
- `GET /api/session/status/{session_id}` - Check session status
- `POST /api/notify` - Send notification and wait for response
- `POST /bot/webhook` - Telegram bot webhook endpoint

## Security

- Session IDs are cryptographically secure UUIDs
- All communication uses HTTPS in production
- User authentication via Telegram user ID mapping
- Optional API key authentication for server endpoints

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Open an issue on GitHub