from fastapi import Fast<PERSON><PERSON>, HTTPException, Request
import uvicorn
import redis
import os
import asyncio
import telegram
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Callback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Message<PERSON><PERSON><PERSON>, Filters

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

app = FastAPI()

# Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
USE_MEMORY_STORAGE = os.getenv('USE_MEMORY_STORAGE', 'false').lower() == 'true'

# Initialize storage
if USE_MEMORY_STORAGE:
    # Use in-memory storage for development
    sessions = {}
    pending_requests = {}
else:
    # Use Redis for production
    redis_client = redis.from_url(REDIS_URL)

# Initialize Telegram Bot
bot = telegram.Bot(token=TELEGRAM_BOT_TOKEN)

@app.post("/api/session/register")
async def register_session(session_data: dict):
    session_id = session_data.get("session_id")
    telegram_user_id = session_data.get("telegram_user_id")
    
    if not session_id or not telegram_user_id:
        raise HTTPException(status_code=400, detail="Missing session_id or telegram_user_id")
    
    if USE_MEMORY_STORAGE:
        sessions[session_id] = telegram_user_id
    else:
        redis_client.set(f"session:{session_id}", telegram_user_id)
    
    return {"message": "Session registered successfully"}

@app.get("/api/session/status/{session_id}")
async def get_session_status(session_id: str):
    if USE_MEMORY_STORAGE:
        active = session_id in sessions
    else:
        active = redis_client.exists(f"session:{session_id}")
    
    return {"active": active}

@app.post("/api/notify")
async def notify_user(notification_data: dict):
    session_id = notification_data.get("session_id")
    question = notification_data.get("question")
    
    if not session_id or not question:
        raise HTTPException(status_code=400, detail="Missing session_id or question")
    
    # Get telegram_user_id from session_id
    if USE_MEMORY_STORAGE:
        telegram_user_id = sessions.get(session_id)
    else:
        telegram_user_id = redis_client.get(f"session:{session_id}")
    
    if not telegram_user_id:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Create a future to wait for the user's response
    future = asyncio.Future()
    if USE_MEMORY_STORAGE:
        pending_requests[session_id] = future
    else:
        # For Redis, we'll need a more complex mechanism, possibly using Redis pub/sub
        # For simplicity in this example, we'll stick with in-memory for pending requests
        pending_requests[session_id] = future
    
    # Send message to user via Telegram
    keyboard = [
        [InlineKeyboardButton("Yes", callback_data="y"),
         InlineKeyboardButton("No", callback_data="n")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await bot.send_message(
        chat_id=telegram_user_id,
        text=f"Request from CLI: {question}",
        reply_markup=reply_markup
    )
    
    # Wait for the user's response
    try:
        answer = await asyncio.wait_for(future, timeout=300.0)  # 5 minutes timeout
        return {"answer": answer}
    except asyncio.TimeoutError:
        # Send a default response if timeout occurs
        return {"answer": "n"}

@app.post("/bot/webhook")
async def telegram_webhook(request: Request):
    # Parse the update
    update_data = await request.json()
    update = Update.de_json(update_data, bot)
    
    # Handle different types of updates
    if update.message and update.message.text and update.message.text.startswith('/start'):
        # Handle /start command
        try:
            _, session_id = update.message.text.split(' ', 1)
        except ValueError:
            await update.message.reply_text("Invalid /start command. Usage: /start <session_id>")
            return {"message": "Invalid command"}
        
        telegram_user_id = update.message.from_user.id
        
        # Register the session
        if USE_MEMORY_STORAGE:
            sessions[session_id] = telegram_user_id
        else:
            redis_client.set(f"session:{session_id}", telegram_user_id)
        
        await update.message.reply_text("Session activated! You will now receive interactive requests here.")
        return {"message": "Session activated"}
    
    elif update.callback_query:
        # Handle button presses
        callback_query = update.callback_query
        telegram_user_id = callback_query.from_user.id
        answer = callback_query.data
        
        # Find the session_id for this user
        session_id = None
        if USE_MEMORY_STORAGE:
            for sid, uid in sessions.items():
                if uid == telegram_user_id:
                    session_id = sid
                    break
        else:
            # This is a simplified approach; in production, you might want a reverse mapping
            for key in redis_client.scan_iter(match="session:*"):
                if redis_client.get(key).decode() == str(telegram_user_id):
                    session_id = key.decode().split(":", 1)[1]
                    break
        
        if session_id and session_id in pending_requests:
            # Resolve the pending request
            future = pending_requests.pop(session_id)
            if not future.done():
                future.set_result(answer)
            
            # Acknowledge the callback query
            await bot.answer_callback_query(callback_query.id, text=f"Answered: {answer}")
            
            # Update the message to show the response
            await bot.edit_message_text(
                text=f"Answered: {answer}",
                chat_id=callback_query.message.chat_id,
                message_id=callback_query.message.message_id,
                reply_markup=None
            )
        else:
            await bot.answer_callback_query(callback_query.id, text="Session not found or expired.")
        
        return {"message": "Callback handled"}
    
    return {"message": "Unhandled update"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)