#!/usr/bin/env python3

import argparse
import subprocess
import sys
import uuid
import requests
import time
import threading
import re
import os

# Configuration - These should ideally come from a config file or environment variables
MCP_SERVER_URL = os.getenv('MCP_SERVER_URL', 'http://localhost:8000')
SESSION_CHECK_INTERVAL = 2  # seconds

class MCPClient:
    def __init__(self, cli_command):
        self.session_id = str(uuid.uuid4())
        self.cli_command = cli_command
        self.process = None
        self.stdout_thread = None
        self.stderr_thread = None
        self.mcp_server_url = MCP_SERVER_URL

    def start_session(self):
        print(f"[MCP] Welcome to the Mobile Interactive Programming Assistant!")
        print(f"[MCP] Session ID generated: {self.session_id}")
        print(f"[MCP] Please send the following command to your Telegram bot to activate this session:")
        print(f"[MCP] /start {self.session_id}")
        print(f"[MCP] Waiting for activation...")

        # Wait for session activation
        while not self.is_session_active():
            time.sleep(SESSION_CHECK_INTERVAL)
        
        print(f"[MCP] Session activated. Starting CLI tool...")

    def is_session_active(self):
        try:
            response = requests.get(f"{self.mcp_server_url}/api/session/status/{self.session_id}")
            return response.status_code == 200 and response.json().get('active', False)
        except requests.RequestException:
            return False

    def start_cli_process(self):
        try:
            # Start the CLI process
            self.process = subprocess.Popen(
                self.cli_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Start threads to handle stdout and stderr
            self.stdout_thread = threading.Thread(target=self._handle_stdout)
            self.stderr_thread = threading.Thread(target=self._handle_stderr)
            self.stdout_thread.daemon = True
            self.stderr_thread.daemon = True
            self.stdout_thread.start()
            self.stderr_thread.start()
            
            # Wait for the process to complete
            self.process.wait()
            
        except Exception as e:
            print(f"[MCP] Error starting CLI process: {e}")
            sys.exit(1)

    def _handle_stdout(self):
        try:
            # Regular expression to detect interactive prompts
            # This is a simple example; you might need to adjust it based on your CLI tool's output
            interactive_prompt_pattern = re.compile(r'.*\[Y/n\].*|.*\(y/n\)\?|.*Please enter your choice:.*')
            
            for line in iter(self.process.stdout.readline, ''):
                if interactive_prompt_pattern.match(line):
                    # Interactive prompt detected, send notification
                    self._send_notification(line.strip())
                else:
                    # Print the line to the user's terminal
                    print(line, end='')
                    
                # Check if the process is still running
                if self.process.poll() is not None:
                    break
                    
        except Exception as e:
            print(f"[MCP] Error handling stdout: {e}")

    def _handle_stderr(self):
        try:
            for line in iter(self.process.stderr.readline, ''):
                print(line, end='', file=sys.stderr)
                
                # Check if the process is still running
                if self.process.poll() is not None:
                    break
                    
        except Exception as e:
            print(f"[MCP] Error handling stderr: {e}")

    def _send_notification(self, question):
        try:
            # Send a POST request to the MCP server
            response = requests.post(
                f"{self.mcp_server_url}/api/notify",
                json={
                    "session_id": self.session_id,
                    "question": question
                },
                timeout=300  # 5 minutes timeout
            )
            
            if response.status_code == 200:
                answer = response.json().get('answer', '')
                # Send the answer to the CLI process
                self.process.stdin.write(answer + '\n')
                self.process.stdin.flush()
            else:
                print(f"[MCP] Failed to get response from server: {response.status_code}")
                # Send a default response to the CLI process to prevent hanging
                self.process.stdin.write('n\n')
                self.process.stdin.flush()
                
        except requests.Timeout:
            print("[MCP] Request to server timed out. Sending default response 'n'.")
            self.process.stdin.write('n\n')
            self.process.stdin.flush()
        except Exception as e:
            print(f"[MCP] Error sending notification: {e}")
            # Send a default response to the CLI process to prevent hanging
            self.process.stdin.write('n\n')
            self.process.stdin.flush()

    def run(self):
        self.start_session()
        self.start_cli_process()

def main():
    parser = argparse.ArgumentParser(description='MCP CLI Wrapper')
    parser.add_argument('cli_command', nargs=argparse.REMAINDER, help='The CLI command to run')
    
    args = parser.parse_args()
    
    if not args.cli_command:
        print("Usage: mcp_cli.py <cli_command>")
        sys.exit(1)
        
    client = MCPClient(args.cli_command)
    client.run()

if __name__ == '__main__':
    main()