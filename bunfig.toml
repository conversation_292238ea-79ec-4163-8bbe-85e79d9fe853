# Bun configuration file
# https://bun.sh/docs/runtime/bunfig

[install]
# Configure package installation
cache = true
exact = false
production = false
optional = true

# Registry configuration
registry = "https://registry.npmjs.org"

[install.scopes]
# Add scoped registries if needed
# "@myorg" = "https://npm.myorg.com"

[run]
# Configure script execution
shell = "system"

# Environment variables for development
[env]
NODE_ENV = "development"

[test]
# Test configuration
coverage = true
