{"name": "codeim", "version": "1.0.0", "description": "AI code CLI ask from IM - A lightweight bridge that enables remote interaction with AI CLI tools through mobile messaging platforms.", "main": "mcp_server.py", "type": "module", "scripts": {"start": "python3 mcp_server.py", "dev": "bun run scripts/dev.ts", "server": "bun run start", "server:dev": "bun run dev", "install:python": "pip3 install -r requirements.txt", "setup": "bun run scripts/setup.ts", "test": "python3 -m pytest tests/ -v", "lint": "python3 -m flake8 *.py", "format": "python3 -m black *.py", "clean": "rm -rf dist/ node_modules/ __pycache__/ *.pyc", "check": "bun run lint && bun run test"}, "dependencies": {}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"bun": ">=1.0.0", "python": ">=3.8"}, "keywords": ["mcp", "cli", "telegram", "mobile", "ai", "bun", "python", "bridge"], "author": "Your Name", "license": "MIT", "packageManager": "bun@1.0.0"}