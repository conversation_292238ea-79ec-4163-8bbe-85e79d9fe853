{"name": "mcp-mobile-cli-bridge", "version": "1.0.0", "description": "A lightweight bridge that enables remote interaction with AI CLI tools through mobile messaging platforms.", "main": "mcp_server.py", "scripts": {"start": "python mcp_server.py", "dev": "export USE_MEMORY_STORAGE=true && python mcp_server.py"}, "dependencies": {"fastapi": "^0.68.0", "uvicorn": "^0.15.0", "python-telegram-bot": "^13.7", "redis": "^3.5.3", "requests": "^2.25.1"}, "devDependencies": {}, "keywords": ["mcp", "cli", "telegram", "mobile", "ai"], "author": "Your Name", "license": "MIT"}