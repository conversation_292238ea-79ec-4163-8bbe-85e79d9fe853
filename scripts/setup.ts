#!/usr/bin/env bun

/**
 * Setup script for codeim project
 * Installs both Python and JavaScript dependencies
 */

import { $ } from "bun";
import { existsSync } from "fs";

console.log("🚀 Setting up codeim project...\n");

// Check if Python is available
let pythonCmd = "python3";
try {
  await $`python3 --version`;
  console.log("✅ Python3 is available");
} catch (error) {
  try {
    await $`python --version`;
    pythonCmd = "python";
    console.log("✅ Python is available");
  } catch (error2) {
    console.error("❌ Python is not available. Please install Python 3.8+");
    process.exit(1);
  }
}

// Check if pip is available
let pipCmd = "pip3";
try {
  await $`pip3 --version`;
  console.log("✅ pip3 is available");
} catch (error) {
  try {
    await $`pip --version`;
    pipCmd = "pip";
    console.log("✅ pip is available");
  } catch (error2) {
    console.error("❌ pip is not available. Please install pip");
    process.exit(1);
  }
}

// Install Python dependencies
console.log("\n📦 Installing Python dependencies...");
if (existsSync("requirements.txt")) {
  try {
    await $`${pipCmd} install -r requirements.txt`;
    console.log("✅ Python dependencies installed");
  } catch (error) {
    console.error("❌ Failed to install Python dependencies:", error);
    process.exit(1);
  }
} else {
  console.log("⚠️  requirements.txt not found, skipping Python dependencies");
}

// Install JavaScript/TypeScript dependencies
console.log("\n📦 Installing JavaScript/TypeScript dependencies...");
try {
  await $`bun install`;
  console.log("✅ JavaScript/TypeScript dependencies installed");
} catch (error) {
  console.error("❌ Failed to install JavaScript/TypeScript dependencies:", error);
  process.exit(1);
}

console.log("\n🎉 Setup completed successfully!");
console.log("\nNext steps:");
console.log("1. Configure your environment variables (see README.md)");
console.log("2. Start the server: bun run dev");
console.log("3. Run CLI wrapper: python mcp_cli.py <command>");
