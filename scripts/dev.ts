#!/usr/bin/env bun

/**
 * Development server script for codeim
 * Starts the MCP server with development settings
 */

import { $ } from "bun";

console.log("🚀 Starting codeim development server...\n");

// Set development environment variables
process.env.USE_MEMORY_STORAGE = "true";
process.env.NODE_ENV = "development";

console.log("📋 Development Configuration:");
console.log("  - Memory storage: enabled");
console.log("  - Redis: disabled");
console.log("  - Environment: development");
console.log("");

// Detect Python command
let pythonCmd = "python3";
try {
  await $`python3 --version`.quiet();
} catch {
  try {
    await $`python --version`.quiet();
    pythonCmd = "python";
  } catch {
    console.error("❌ Python not found. Please install Python 3.8+");
    process.exit(1);
  }
}

try {
  // Start the Python MCP server
  console.log(`🐍 Starting MCP server with ${pythonCmd}...`);
  await $`${pythonCmd} mcp_server.py`;
} catch (error) {
  console.error("❌ Failed to start development server:", error);
  process.exit(1);
}
